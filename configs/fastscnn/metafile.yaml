Collections:
- Name: FastSCNN
  License: Apache License 2.0
  Metadata:
    Training Data:
    - Cityscapes
  Paper:
    Title: Fast-SCNN for Semantic Segmentation
    URL: https://arxiv.org/abs/1902.04502
  README: configs/fastscnn/README.md
  Frameworks:
  - PyTorch
Models:
- Name: fast_scnn_8xb4-160k_cityscapes-512x1024
  In Collection: FastSCNN
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 70.96
      mIoU(ms+flip): 72.65
  Config: configs/fastscnn/fast_scnn_8xb4-160k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 32
    Architecture:
    - FastSCNN
    - FastSCNN
    Training Resources: 8x V100 GPUS
    Memory (GB): 3.3
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/fast_scnn/fast_scnn_lr0.12_8x4_160k_cityscapes/fast_scnn_lr0.12_8x4_160k_cityscapes_20210630_164853-0cec9937.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/fast_scnn/fast_scnn_lr0.12_8x4_160k_cityscapes/fast_scnn_lr0.12_8x4_160k_cityscapes_20210630_164853.log.json
  Paper:
    Title: Fast-SCNN for Semantic Segmentation
    URL: https://arxiv.org/abs/1902.04502
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/backbones/fast_scnn.py#L272
  Framework: PyTorch
