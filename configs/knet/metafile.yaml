Collections:
- Name: KNet
  License: Apache License 2.0
  Metadata:
    Training Data:
    - ADE20K
  Paper:
    Title: 'K-Net: Towards Unified Image Segmentation'
    URL: https://arxiv.org/abs/2106.14855
  README: configs/knet/README.md
  Frameworks:
  - PyTorch
Models:
- Name: knet-s3_r50-d8_fcn_8xb2-adamw-80k_ade20k-512x512
  In Collection: KNet
  Results:
    Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 43.6
      mIoU(ms+flip): 45.12
  Config: configs/knet/knet-s3_r50-d8_fcn_8xb2-adamw-80k_ade20k-512x512.py
  Metadata:
    Training Data: ADE20K
    Batch Size: 16
    Architecture:
    - R-50-D8
    - KNet
    - FCN
    Training Resources: 8x V100 GPUS
    Memory (GB): 7.01
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/knet/knet_s3_fcn_r50-d8_8x2_512x512_adamw_80k_ade20k/knet_s3_fcn_r50-d8_8x2_512x512_adamw_80k_ade20k_20220228_043751-abcab920.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/knet/knet_s3_fcn_r50-d8_8x2_512x512_adamw_80k_ade20k/knet_s3_fcn_r50-d8_8x2_512x512_adamw_80k_ade20k_20220228_043751.log.json
  Paper:
    Title: 'K-Net: Towards Unified Image Segmentation'
    URL: https://arxiv.org/abs/2106.14855
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.23.0/mmseg/models/decode_heads/knet_head.py#L392
  Framework: PyTorch
- Name: knet-s3_r50-d8_pspnet_8xb2-adamw-80k_ade20k-512x512
  In Collection: KNet
  Results:
    Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 44.18
      mIoU(ms+flip): 45.58
  Config: configs/knet/knet-s3_r50-d8_pspnet_8xb2-adamw-80k_ade20k-512x512.py
  Metadata:
    Training Data: ADE20K
    Batch Size: 16
    Architecture:
    - R-50-D8
    - KNet
    - PSPNet
    Training Resources: 8x V100 GPUS
    Memory (GB): 6.98
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/knet/knet_s3_pspnet_r50-d8_8x2_512x512_adamw_80k_ade20k/knet_s3_pspnet_r50-d8_8x2_512x512_adamw_80k_ade20k_20220228_054634-d2c72240.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/knet/knet_s3_pspnet_r50-d8_8x2_512x512_adamw_80k_ade20k/knet_s3_pspnet_r50-d8_8x2_512x512_adamw_80k_ade20k_20220228_054634.log.json
  Paper:
    Title: 'K-Net: Towards Unified Image Segmentation'
    URL: https://arxiv.org/abs/2106.14855
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.23.0/mmseg/models/decode_heads/knet_head.py#L392
  Framework: PyTorch
- Name: knet-s3_r50-d8_deeplabv3_8xb2-adamw-80k_ade20k-512x512
  In Collection: KNet
  Results:
    Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 45.06
      mIoU(ms+flip): 46.11
  Config: configs/knet/knet-s3_r50-d8_deeplabv3_8xb2-adamw-80k_ade20k-512x512.py
  Metadata:
    Training Data: ADE20K
    Batch Size: 16
    Architecture:
    - R-50-D8
    - KNet
    - DeepLabV3
    Training Resources: 8x V100 GPUS
    Memory (GB): 7.42
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/knet/knet_s3_deeplabv3_r50-d8_8x2_512x512_adamw_80k_ade20k/knet_s3_deeplabv3_r50-d8_8x2_512x512_adamw_80k_ade20k_20220228_041642-00c8fbeb.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/knet/knet_s3_deeplabv3_r50-d8_8x2_512x512_adamw_80k_ade20k/knet_s3_deeplabv3_r50-d8_8x2_512x512_adamw_80k_ade20k_20220228_041642.log.json
  Paper:
    Title: 'K-Net: Towards Unified Image Segmentation'
    URL: https://arxiv.org/abs/2106.14855
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.23.0/mmseg/models/decode_heads/knet_head.py#L392
  Framework: PyTorch
- Name: knet-s3_r50-d8_upernet_8xb2-adamw-80k_ade20k-512x512
  In Collection: KNet
  Results:
    Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 43.45
      mIoU(ms+flip): 44.07
  Config: configs/knet/knet-s3_r50-d8_upernet_8xb2-adamw-80k_ade20k-512x512.py
  Metadata:
    Training Data: ADE20K
    Batch Size: 16
    Architecture:
    - R-50-D8
    - KNet
    - UperNet
    Training Resources: 8x V100 GPUS
    Memory (GB): 7.34
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/knet/knet_s3_upernet_r50-d8_8x2_512x512_adamw_80k_ade20k/knet_s3_upernet_r50-d8_8x2_512x512_adamw_80k_ade20k_20220304_125657-215753b0.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/knet/knet_s3_upernet_r50-d8_8x2_512x512_adamw_80k_ade20k/knet_s3_upernet_r50-d8_8x2_512x512_adamw_80k_ade20k_20220304_125657.log.json
  Paper:
    Title: 'K-Net: Towards Unified Image Segmentation'
    URL: https://arxiv.org/abs/2106.14855
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.23.0/mmseg/models/decode_heads/knet_head.py#L392
  Framework: PyTorch
- Name: knet-s3_swin-t_upernet_8xb2-adamw-80k_ade20k-512x512
  In Collection: KNet
  Results:
    Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 45.84
      mIoU(ms+flip): 46.27
  Config: configs/knet/knet-s3_swin-t_upernet_8xb2-adamw-80k_ade20k-512x512.py
  Metadata:
    Training Data: ADE20K
    Batch Size: 16
    Architecture:
    - Swin-T
    - KNet
    - UperNet
    Training Resources: 8x V100 GPUS
    Memory (GB): 7.57
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/knet/knet_s3_upernet_swin-t_8x2_512x512_adamw_80k_ade20k/knet_s3_upernet_swin-t_8x2_512x512_adamw_80k_ade20k_20220303_133059-7545e1dc.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/knet/knet_s3_upernet_swin-t_8x2_512x512_adamw_80k_ade20k/knet_s3_upernet_swin-t_8x2_512x512_adamw_80k_ade20k_20220303_133059.log.json
  Paper:
    Title: 'K-Net: Towards Unified Image Segmentation'
    URL: https://arxiv.org/abs/2106.14855
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.23.0/mmseg/models/decode_heads/knet_head.py#L392
  Framework: PyTorch
- Name: knet-s3_swin-l_upernet_8xb2-adamw-80k_ade20k-512x512
  In Collection: KNet
  Results:
    Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 52.05
      mIoU(ms+flip): 53.24
  Config: configs/knet/knet-s3_swin-l_upernet_8xb2-adamw-80k_ade20k-512x512.py
  Metadata:
    Training Data: ADE20K
    Batch Size: 16
    Architecture:
    - Swin-L
    - KNet
    - UperNet
    Training Resources: 8x V100 GPUS
    Memory (GB): 13.5
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/knet/knet_s3_upernet_swin-l_8x2_512x512_adamw_80k_ade20k/knet_s3_upernet_swin-l_8x2_512x512_adamw_80k_ade20k_20220303_154559-d8da9a90.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/knet/knet_s3_upernet_swin-l_8x2_512x512_adamw_80k_ade20k/knet_s3_upernet_swin-l_8x2_512x512_adamw_80k_ade20k_20220303_154559.log.json
  Paper:
    Title: 'K-Net: Towards Unified Image Segmentation'
    URL: https://arxiv.org/abs/2106.14855
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.23.0/mmseg/models/decode_heads/knet_head.py#L392
  Framework: PyTorch
- Name: knet-s3_swin-l_upernet_8xb2-adamw-80k_ade20k-640x640
  In Collection: KNet
  Results:
    Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 52.21
      mIoU(ms+flip): 53.34
  Config: configs/knet/knet-s3_swin-l_upernet_8xb2-adamw-80k_ade20k-640x640.py
  Metadata:
    Training Data: ADE20K
    Batch Size: 16
    Architecture:
    - Swin-L
    - KNet
    - UperNet
    Training Resources: 8x V100 GPUS
    Memory (GB): 13.54
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/knet/knet_s3_upernet_swin-l_8x2_640x640_adamw_80k_ade20k/knet_s3_upernet_swin-l_8x2_640x640_adamw_80k_ade20k_20220301_220747-8787fc71.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/knet/knet_s3_upernet_swin-l_8x2_640x640_adamw_80k_ade20k/knet_s3_upernet_swin-l_8x2_640x640_adamw_80k_ade20k_20220301_220747.log.json
  Paper:
    Title: 'K-Net: Towards Unified Image Segmentation'
    URL: https://arxiv.org/abs/2106.14855
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.23.0/mmseg/models/decode_heads/knet_head.py#L392
  Framework: PyTorch
