Collections:
- Name: STDC
  License: Apache License 2.0
  Metadata:
    Training Data:
    - Cityscapes
  Paper:
    Title: Rethinking BiSeNet For Real-time Semantic Segmentation
    URL: https://arxiv.org/abs/2104.13188
  README: configs/stdc/README.md
  Frameworks:
  - PyTorch
Models:
- Name: stdc1_4xb12-80k_cityscapes-512x1024
  In Collection: STDC
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 71.82
      mIoU(ms+flip): 73.89
  Config: configs/stdc/stdc1_4xb12-80k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 48
    Architecture:
    - STDC1
    - STDC
    Training Resources: 4x V100 GPUS
    Memory (GB): 7.15
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/stdc/stdc1_512x1024_80k_cityscapes/stdc1_512x1024_80k_cityscapes_20220224_073048-74e6920a.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/stdc/stdc1_512x1024_80k_cityscapes/stdc1_512x1024_80k_cityscapes_20220224_073048.log.json
  Paper:
    Title: Rethinking BiSeNet For Real-time Semantic Segmentation
    URL: https://arxiv.org/abs/2104.13188
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.20.0/mmseg/models/backbones/stdc.py#L394
  Framework: PyTorch
- Name: stdc1_in1k-pre_4xb12-80k_cityscapes-512x1024
  In Collection: STDC
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 74.94
      mIoU(ms+flip): 76.97
  Config: configs/stdc/stdc1_in1k-pre_4xb12-80k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 48
    Architecture:
    - STDC1
    - STDC
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/stdc/stdc1_in1k-pre_512x1024_80k_cityscapes/stdc1_in1k-pre_512x1024_80k_cityscapes_20220224_141648-3d4c2981.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/stdc/stdc1_in1k-pre_512x1024_80k_cityscapes/stdc1_in1k-pre_512x1024_80k_cityscapes_20220224_141648.log.json
  Paper:
    Title: Rethinking BiSeNet For Real-time Semantic Segmentation
    URL: https://arxiv.org/abs/2104.13188
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.20.0/mmseg/models/backbones/stdc.py#L394
  Framework: PyTorch
- Name: stdc2_4xb12-80k_cityscapes-512x1024
  In Collection: STDC
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 73.15
      mIoU(ms+flip): 76.13
  Config: configs/stdc/stdc2_4xb12-80k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 48
    Architecture:
    - STDC2
    - STDC
    Training Resources: 4x V100 GPUS
    Memory (GB): 8.27
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/stdc/stdc2_512x1024_80k_cityscapes/stdc2_512x1024_80k_cityscapes_20220222_132015-fb1e3a1a.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/stdc/stdc2_512x1024_80k_cityscapes/stdc2_512x1024_80k_cityscapes_20220222_132015.log.json
  Paper:
    Title: Rethinking BiSeNet For Real-time Semantic Segmentation
    URL: https://arxiv.org/abs/2104.13188
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.20.0/mmseg/models/backbones/stdc.py#L394
  Framework: PyTorch
- Name: stdc2_in1k-pre_4xb12-80k_cityscapes-512x1024
  In Collection: STDC
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 76.67
      mIoU(ms+flip): 78.67
  Config: configs/stdc/stdc2_in1k-pre_4xb12-80k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 48
    Architecture:
    - STDC2
    - STDC
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/stdc/stdc2_in1k-pre_512x1024_80k_cityscapes/stdc2_in1k-pre_512x1024_80k_cityscapes_20220224_073048-1f8f0f6c.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/stdc/stdc2_in1k-pre_512x1024_80k_cityscapes/stdc2_in1k-pre_512x1024_80k_cityscapes_20220224_073048.log.json
  Paper:
    Title: Rethinking BiSeNet For Real-time Semantic Segmentation
    URL: https://arxiv.org/abs/2104.13188
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.20.0/mmseg/models/backbones/stdc.py#L394
  Framework: PyTorch
