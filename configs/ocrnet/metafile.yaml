Collections:
- Name: OCRNet
  License: Apache License 2.0
  Metadata:
    Training Data:
    - Cityscapes
    - '# HRNet backbone'
    - '# ResNet backbone'
    - ADE20K
    - Pascal VOC 2012 + Aug
  Paper:
    Title: Object-Contextual Representations for Semantic Segmentation
    URL: https://arxiv.org/abs/1909.11065
  README: configs/ocrnet/README.md
  Frameworks:
  - PyTorch
Models:
- Name: ocrnet_hr18s_4xb2-40k_cityscapes-512x1024
  In Collection: OCRNet
  Results:
    Task: Semantic Segmentation
    Dataset: '# HRNet backbone'
    Metrics:
      mIoU: 76.61
      mIoU(ms+flip): 78.01
  Config: configs/ocrnet/ocrnet_hr18s_4xb2-40k_cityscapes-512x1024.py
  Metadata:
    Training Data: '# HRNet backbone'
    Batch Size: 8
    Architecture:
    - HRNetV2p-W18-Small
    - OCRNet
    Training Resources: 4x A100 GPUS
    Memory (GB): 3.5
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr18s_4xb2-40k_cityscapes-512x1024/ocrnet_hr18s_4xb2-40k_cityscapes-512x1024_20230227_145026-6c052a14.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr18s_4xb2-40k_cityscapes-512x1024/ocrnet_hr18s_4xb2-40k_cityscapes-512x1024_20230227_145026.json
  Paper:
    Title: Object-Contextual Representations for Semantic Segmentation
    URL: https://arxiv.org/abs/1909.11065
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/ocr_head.py#L86
  Framework: PyTorch
- Name: ocrnet_hr18_4xb2-40k_cityscapes-512x1024
  In Collection: OCRNet
  Results:
    Task: Semantic Segmentation
    Dataset: '# HRNet backbone'
    Metrics:
      mIoU: 77.72
      mIoU(ms+flip): 79.49
  Config: configs/ocrnet/ocrnet_hr18_4xb2-40k_cityscapes-512x1024.py
  Metadata:
    Training Data: '# HRNet backbone'
    Batch Size: 8
    Architecture:
    - HRNetV2p-W18
    - OCRNet
    Training Resources: 4x V100 GPUS
    Memory (GB): 4.7
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr18_512x1024_40k_cityscapes/ocrnet_hr18_512x1024_40k_cityscapes_20200601_033320-401c5bdd.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr18_512x1024_40k_cityscapes/ocrnet_hr18_512x1024_40k_cityscapes_20200601_033320.log.json
  Paper:
    Title: Object-Contextual Representations for Semantic Segmentation
    URL: https://arxiv.org/abs/1909.11065
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/ocr_head.py#L86
  Framework: PyTorch
- Name: ocrnet_hr48_4xb2-40k_cityscapes-512x1024
  In Collection: OCRNet
  Results:
    Task: Semantic Segmentation
    Dataset: '# HRNet backbone'
    Metrics:
      mIoU: 80.58
      mIoU(ms+flip): 81.79
  Config: configs/ocrnet/ocrnet_hr48_4xb2-40k_cityscapes-512x1024.py
  Metadata:
    Training Data: '# HRNet backbone'
    Batch Size: 8
    Architecture:
    - HRNetV2p-W48
    - OCRNet
    Training Resources: 4x V100 GPUS
    Memory (GB): 8.0
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr48_512x1024_40k_cityscapes/ocrnet_hr48_512x1024_40k_cityscapes_20200601_033336-55b32491.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr48_512x1024_40k_cityscapes/ocrnet_hr48_512x1024_40k_cityscapes_20200601_033336.log.json
  Paper:
    Title: Object-Contextual Representations for Semantic Segmentation
    URL: https://arxiv.org/abs/1909.11065
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/ocr_head.py#L86
  Framework: PyTorch
- Name: ocrnet_hr18s_4xb2-80k_cityscapes-512x1024
  In Collection: OCRNet
  Results:
    Task: Semantic Segmentation
    Dataset: '# HRNet backbone'
    Metrics:
      mIoU: 77.16
      mIoU(ms+flip): 78.66
  Config: configs/ocrnet/ocrnet_hr18s_4xb2-80k_cityscapes-512x1024.py
  Metadata:
    Training Data: '# HRNet backbone'
    Batch Size: 8
    Architecture:
    - HRNetV2p-W18-Small
    - OCRNet
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr18s_512x1024_80k_cityscapes/ocrnet_hr18s_512x1024_80k_cityscapes_20200601_222735-55979e63.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr18s_512x1024_80k_cityscapes/ocrnet_hr18s_512x1024_80k_cityscapes_20200601_222735.log.json
  Paper:
    Title: Object-Contextual Representations for Semantic Segmentation
    URL: https://arxiv.org/abs/1909.11065
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/ocr_head.py#L86
  Framework: PyTorch
- Name: ocrnet_hr18_4xb2-80k_cityscapes-512x1024
  In Collection: OCRNet
  Results:
    Task: Semantic Segmentation
    Dataset: '# HRNet backbone'
    Metrics:
      mIoU: 78.57
      mIoU(ms+flip): 80.46
  Config: configs/ocrnet/ocrnet_hr18_4xb2-80k_cityscapes-512x1024.py
  Metadata:
    Training Data: '# HRNet backbone'
    Batch Size: 8
    Architecture:
    - HRNetV2p-W18
    - OCRNet
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr18_512x1024_80k_cityscapes/ocrnet_hr18_512x1024_80k_cityscapes_20200614_230521-c2e1dd4a.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr18_512x1024_80k_cityscapes/ocrnet_hr18_512x1024_80k_cityscapes_20200614_230521.log.json
  Paper:
    Title: Object-Contextual Representations for Semantic Segmentation
    URL: https://arxiv.org/abs/1909.11065
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/ocr_head.py#L86
  Framework: PyTorch
- Name: ocrnet_hr48_4xb2-80k_cityscapes-512x1024
  In Collection: OCRNet
  Results:
    Task: Semantic Segmentation
    Dataset: '# HRNet backbone'
    Metrics:
      mIoU: 80.7
      mIoU(ms+flip): 81.87
  Config: configs/ocrnet/ocrnet_hr48_4xb2-80k_cityscapes-512x1024.py
  Metadata:
    Training Data: '# HRNet backbone'
    Batch Size: 8
    Architecture:
    - HRNetV2p-W48
    - OCRNet
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr48_512x1024_80k_cityscapes/ocrnet_hr48_512x1024_80k_cityscapes_20200601_222752-9076bcdf.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr48_512x1024_80k_cityscapes/ocrnet_hr48_512x1024_80k_cityscapes_20200601_222752.log.json
  Paper:
    Title: Object-Contextual Representations for Semantic Segmentation
    URL: https://arxiv.org/abs/1909.11065
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/ocr_head.py#L86
  Framework: PyTorch
- Name: ocrnet_hr18s_4xb2-160k_cityscapes-512x1024
  In Collection: OCRNet
  Results:
    Task: Semantic Segmentation
    Dataset: '# HRNet backbone'
    Metrics:
      mIoU: 78.45
      mIoU(ms+flip): 79.97
  Config: configs/ocrnet/ocrnet_hr18s_4xb2-160k_cityscapes-512x1024.py
  Metadata:
    Training Data: '# HRNet backbone'
    Batch Size: 8
    Architecture:
    - HRNetV2p-W18-Small
    - OCRNet
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr18s_512x1024_160k_cityscapes/ocrnet_hr18s_512x1024_160k_cityscapes_20200602_191005-f4a7af28.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr18s_512x1024_160k_cityscapes/ocrnet_hr18s_512x1024_160k_cityscapes_20200602_191005.log.json
  Paper:
    Title: Object-Contextual Representations for Semantic Segmentation
    URL: https://arxiv.org/abs/1909.11065
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/ocr_head.py#L86
  Framework: PyTorch
- Name: ocrnet_hr18_4xb2-160k_cityscapes-512x1024
  In Collection: OCRNet
  Results:
    Task: Semantic Segmentation
    Dataset: '# HRNet backbone'
    Metrics:
      mIoU: 79.47
      mIoU(ms+flip): 80.91
  Config: configs/ocrnet/ocrnet_hr18_4xb2-160k_cityscapes-512x1024.py
  Metadata:
    Training Data: '# HRNet backbone'
    Batch Size: 8
    Architecture:
    - HRNetV2p-W18
    - OCRNet
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr18_512x1024_160k_cityscapes/ocrnet_hr18_512x1024_160k_cityscapes_20200602_191001-b9172d0c.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr18_512x1024_160k_cityscapes/ocrnet_hr18_512x1024_160k_cityscapes_20200602_191001.log.json
  Paper:
    Title: Object-Contextual Representations for Semantic Segmentation
    URL: https://arxiv.org/abs/1909.11065
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/ocr_head.py#L86
  Framework: PyTorch
- Name: ocrnet_hr48_4xb2-160k_cityscapes-512x1024
  In Collection: OCRNet
  Results:
    Task: Semantic Segmentation
    Dataset: '# HRNet backbone'
    Metrics:
      mIoU: 81.35
      mIoU(ms+flip): 82.7
  Config: configs/ocrnet/ocrnet_hr48_4xb2-160k_cityscapes-512x1024.py
  Metadata:
    Training Data: '# HRNet backbone'
    Batch Size: 8
    Architecture:
    - HRNetV2p-W48
    - OCRNet
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr48_512x1024_160k_cityscapes/ocrnet_hr48_512x1024_160k_cityscapes_20200602_191037-dfbf1b0c.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr48_512x1024_160k_cityscapes/ocrnet_hr48_512x1024_160k_cityscapes_20200602_191037.log.json
  Paper:
    Title: Object-Contextual Representations for Semantic Segmentation
    URL: https://arxiv.org/abs/1909.11065
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/ocr_head.py#L86
  Framework: PyTorch
- Name: ocrnet_r101-d8_4xb2-40k_cityscapes-512x1024
  In Collection: OCRNet
  Results:
    Task: Semantic Segmentation
    Dataset: '# ResNet backbone'
    Metrics:
      mIoU: 80.09
  Config: configs/ocrnet/ocrnet_r101-d8_4xb2-40k_cityscapes-512x1024.py
  Metadata:
    Training Data: '# ResNet backbone'
    Batch Size: 8
    Architecture:
    - R-101-D8
    - OCRNet
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_r101-d8_512x1024_40k_b8_cityscapes/ocrnet_r101-d8_512x1024_40k_b8_cityscapes_20200717_110721-02ac0f13.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_r101-d8_512x1024_40k_b8_cityscapes/ocrnet_r101-d8_512x1024_40k_b8_cityscapes_20200717_110721.log.json
  Paper:
    Title: Object-Contextual Representations for Semantic Segmentation
    URL: https://arxiv.org/abs/1909.11065
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/ocr_head.py#L86
  Framework: PyTorch
- Name: ocrnet_r101-d8_8xb2-40k_cityscapes-512x1024
  In Collection: OCRNet
  Results:
    Task: Semantic Segmentation
    Dataset: '# ResNet backbone'
    Metrics:
      mIoU: 80.3
  Config: configs/ocrnet/ocrnet_r101-d8_8xb2-40k_cityscapes-512x1024.py
  Metadata:
    Training Data: '# ResNet backbone'
    Batch Size: 16
    Architecture:
    - R-101-D8
    - OCRNet
    Training Resources: 8x V100 GPUS
    Memory (GB): 8.8
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_r101-d8_512x1024_40k_b16_cityscapes/ocrnet_r101-d8_512x1024_40k_b16_cityscapes_20200723_193726-db500f80.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_r101-d8_512x1024_40k_b16_cityscapes/ocrnet_r101-d8_512x1024_40k_b16_cityscapes_20200723_193726.log.json
  Paper:
    Title: Object-Contextual Representations for Semantic Segmentation
    URL: https://arxiv.org/abs/1909.11065
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/ocr_head.py#L86
  Framework: PyTorch
- Name: ocrnet_r101-d8_8xb2-80k_cityscapes-512x1024
  In Collection: OCRNet
  Results:
    Task: Semantic Segmentation
    Dataset: '# ResNet backbone'
    Metrics:
      mIoU: 80.81
  Config: configs/ocrnet/ocrnet_r101-d8_8xb2-80k_cityscapes-512x1024.py
  Metadata:
    Training Data: '# ResNet backbone'
    Batch Size: 16
    Architecture:
    - R-101-D8
    - OCRNet
    Training Resources: 8x V100 GPUS
    Memory (GB): 8.8
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_r101-d8_512x1024_80k_b16_cityscapes/ocrnet_r101-d8_512x1024_80k_b16_cityscapes_20200723_192421-78688424.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_r101-d8_512x1024_80k_b16_cityscapes/ocrnet_r101-d8_512x1024_80k_b16_cityscapes_20200723_192421.log.json
  Paper:
    Title: Object-Contextual Representations for Semantic Segmentation
    URL: https://arxiv.org/abs/1909.11065
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/ocr_head.py#L86
  Framework: PyTorch
- Name: ocrnet_hr18s_4xb4-80k_ade20k-512x512
  In Collection: OCRNet
  Results:
    Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 35.06
      mIoU(ms+flip): 35.8
  Config: configs/ocrnet/ocrnet_hr18s_4xb4-80k_ade20k-512x512.py
  Metadata:
    Training Data: ADE20K
    Batch Size: 16
    Architecture:
    - HRNetV2p-W18-Small
    - OCRNet
    Training Resources: 4x V100 GPUS
    Memory (GB): 6.7
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr18s_512x512_80k_ade20k/ocrnet_hr18s_512x512_80k_ade20k_20200615_055600-e80b62af.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr18s_512x512_80k_ade20k/ocrnet_hr18s_512x512_80k_ade20k_20200615_055600.log.json
  Paper:
    Title: Object-Contextual Representations for Semantic Segmentation
    URL: https://arxiv.org/abs/1909.11065
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/ocr_head.py#L86
  Framework: PyTorch
- Name: ocrnet_hr18_4xb4-80k_ade20k-512x512
  In Collection: OCRNet
  Results:
    Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 37.79
      mIoU(ms+flip): 39.16
  Config: configs/ocrnet/ocrnet_hr18_4xb4-80k_ade20k-512x512.py
  Metadata:
    Training Data: ADE20K
    Batch Size: 16
    Architecture:
    - HRNetV2p-W18
    - OCRNet
    Training Resources: 4x V100 GPUS
    Memory (GB): 7.9
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr18_512x512_80k_ade20k/ocrnet_hr18_512x512_80k_ade20k_20200615_053157-d173d83b.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr18_512x512_80k_ade20k/ocrnet_hr18_512x512_80k_ade20k_20200615_053157.log.json
  Paper:
    Title: Object-Contextual Representations for Semantic Segmentation
    URL: https://arxiv.org/abs/1909.11065
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/ocr_head.py#L86
  Framework: PyTorch
- Name: ocrnet_hr48_4xb4-80k_ade20k-512x512
  In Collection: OCRNet
  Results:
    Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 43.0
      mIoU(ms+flip): 44.3
  Config: configs/ocrnet/ocrnet_hr48_4xb4-80k_ade20k-512x512.py
  Metadata:
    Training Data: ADE20K
    Batch Size: 16
    Architecture:
    - HRNetV2p-W48
    - OCRNet
    Training Resources: 4x V100 GPUS
    Memory (GB): 11.2
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr48_512x512_80k_ade20k/ocrnet_hr48_512x512_80k_ade20k_20200615_021518-d168c2d1.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr48_512x512_80k_ade20k/ocrnet_hr48_512x512_80k_ade20k_20200615_021518.log.json
  Paper:
    Title: Object-Contextual Representations for Semantic Segmentation
    URL: https://arxiv.org/abs/1909.11065
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/ocr_head.py#L86
  Framework: PyTorch
- Name: ocrnet_hr18s_4xb4-80k_ade20k-512x512
  In Collection: OCRNet
  Results:
    Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 37.19
      mIoU(ms+flip): 38.4
  Config: configs/ocrnet/ocrnet_hr18s_4xb4-80k_ade20k-512x512.py
  Metadata:
    Training Data: ADE20K
    Batch Size: 16
    Architecture:
    - HRNetV2p-W18-Small
    - OCRNet
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr18s_512x512_160k_ade20k/ocrnet_hr18s_512x512_160k_ade20k_20200615_184505-8e913058.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr18s_512x512_160k_ade20k/ocrnet_hr18s_512x512_160k_ade20k_20200615_184505.log.json
  Paper:
    Title: Object-Contextual Representations for Semantic Segmentation
    URL: https://arxiv.org/abs/1909.11065
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/ocr_head.py#L86
  Framework: PyTorch
- Name: ocrnet_hr18_4xb4-80k_ade20k-512x512
  In Collection: OCRNet
  Results:
    Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 39.32
      mIoU(ms+flip): 40.8
  Config: configs/ocrnet/ocrnet_hr18_4xb4-80k_ade20k-512x512.py
  Metadata:
    Training Data: ADE20K
    Batch Size: 16
    Architecture:
    - HRNetV2p-W18
    - OCRNet
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr18_512x512_160k_ade20k/ocrnet_hr18_512x512_160k_ade20k_20200615_200940-d8fcd9d1.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr18_512x512_160k_ade20k/ocrnet_hr18_512x512_160k_ade20k_20200615_200940.log.json
  Paper:
    Title: Object-Contextual Representations for Semantic Segmentation
    URL: https://arxiv.org/abs/1909.11065
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/ocr_head.py#L86
  Framework: PyTorch
- Name: ocrnet_hr48_4xb4-160k_ade20k-512x512
  In Collection: OCRNet
  Results:
    Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 43.25
      mIoU(ms+flip): 44.88
  Config: configs/ocrnet/ocrnet_hr48_4xb4-160k_ade20k-512x512.py
  Metadata:
    Training Data: ADE20K
    Batch Size: 16
    Architecture:
    - HRNetV2p-W48
    - OCRNet
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr48_512x512_160k_ade20k/ocrnet_hr48_512x512_160k_ade20k_20200615_184705-a073726d.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr48_512x512_160k_ade20k/ocrnet_hr48_512x512_160k_ade20k_20200615_184705.log.json
  Paper:
    Title: Object-Contextual Representations for Semantic Segmentation
    URL: https://arxiv.org/abs/1909.11065
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/ocr_head.py#L86
  Framework: PyTorch
- Name: ocrnet_hr18s_4xb4-20k_voc12aug-512x512
  In Collection: OCRNet
  Results:
    Task: Semantic Segmentation
    Dataset: Pascal VOC 2012 + Aug
    Metrics:
      mIoU: 71.7
      mIoU(ms+flip): 73.84
  Config: configs/ocrnet/ocrnet_hr18s_4xb4-20k_voc12aug-512x512.py
  Metadata:
    Training Data: Pascal VOC 2012 + Aug
    Batch Size: 16
    Architecture:
    - HRNetV2p-W18-Small
    - OCRNet
    Training Resources: 4x V100 GPUS
    Memory (GB): 3.5
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr18s_512x512_20k_voc12aug/ocrnet_hr18s_512x512_20k_voc12aug_20200617_233913-02b04fcb.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr18s_512x512_20k_voc12aug/ocrnet_hr18s_512x512_20k_voc12aug_20200617_233913.log.json
  Paper:
    Title: Object-Contextual Representations for Semantic Segmentation
    URL: https://arxiv.org/abs/1909.11065
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/ocr_head.py#L86
  Framework: PyTorch
- Name: ocrnet_hr18_4xb4-20k_voc12aug-512x512
  In Collection: OCRNet
  Results:
    Task: Semantic Segmentation
    Dataset: Pascal VOC 2012 + Aug
    Metrics:
      mIoU: 74.75
      mIoU(ms+flip): 77.11
  Config: configs/ocrnet/ocrnet_hr18_4xb4-20k_voc12aug-512x512.py
  Metadata:
    Training Data: Pascal VOC 2012 + Aug
    Batch Size: 16
    Architecture:
    - HRNetV2p-W18
    - OCRNet
    Training Resources: 4x V100 GPUS
    Memory (GB): 4.7
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr18_512x512_20k_voc12aug/ocrnet_hr18_512x512_20k_voc12aug_20200617_233932-8954cbb7.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr18_512x512_20k_voc12aug/ocrnet_hr18_512x512_20k_voc12aug_20200617_233932.log.json
  Paper:
    Title: Object-Contextual Representations for Semantic Segmentation
    URL: https://arxiv.org/abs/1909.11065
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/ocr_head.py#L86
  Framework: PyTorch
- Name: ocrnet_hr48_4xb4-20k_voc12aug-512x512
  In Collection: OCRNet
  Results:
    Task: Semantic Segmentation
    Dataset: Pascal VOC 2012 + Aug
    Metrics:
      mIoU: 77.72
      mIoU(ms+flip): 79.87
  Config: configs/ocrnet/ocrnet_hr48_4xb4-20k_voc12aug-512x512.py
  Metadata:
    Training Data: Pascal VOC 2012 + Aug
    Batch Size: 16
    Architecture:
    - HRNetV2p-W48
    - OCRNet
    Training Resources: 4x V100 GPUS
    Memory (GB): 8.1
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr48_512x512_20k_voc12aug/ocrnet_hr48_512x512_20k_voc12aug_20200617_233932-9e82080a.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr48_512x512_20k_voc12aug/ocrnet_hr48_512x512_20k_voc12aug_20200617_233932.log.json
  Paper:
    Title: Object-Contextual Representations for Semantic Segmentation
    URL: https://arxiv.org/abs/1909.11065
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/ocr_head.py#L86
  Framework: PyTorch
- Name: ocrnet_hr18s_4xb4-40k_voc12aug-512x512
  In Collection: OCRNet
  Results:
    Task: Semantic Segmentation
    Dataset: Pascal VOC 2012 + Aug
    Metrics:
      mIoU: 72.76
      mIoU(ms+flip): 74.6
  Config: configs/ocrnet/ocrnet_hr18s_4xb4-40k_voc12aug-512x512.py
  Metadata:
    Training Data: Pascal VOC 2012 + Aug
    Batch Size: 16
    Architecture:
    - HRNetV2p-W18-Small
    - OCRNet
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr18s_512x512_40k_voc12aug/ocrnet_hr18s_512x512_40k_voc12aug_20200614_002025-42b587ac.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr18s_512x512_40k_voc12aug/ocrnet_hr18s_512x512_40k_voc12aug_20200614_002025.log.json
  Paper:
    Title: Object-Contextual Representations for Semantic Segmentation
    URL: https://arxiv.org/abs/1909.11065
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/ocr_head.py#L86
  Framework: PyTorch
- Name: ocrnet_hr18_4xb4-40k_voc12aug-512x512
  In Collection: OCRNet
  Results:
    Task: Semantic Segmentation
    Dataset: Pascal VOC 2012 + Aug
    Metrics:
      mIoU: 74.98
      mIoU(ms+flip): 77.4
  Config: configs/ocrnet/ocrnet_hr18_4xb4-40k_voc12aug-512x512.py
  Metadata:
    Training Data: Pascal VOC 2012 + Aug
    Batch Size: 16
    Architecture:
    - HRNetV2p-W18
    - OCRNet
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr18_512x512_40k_voc12aug/ocrnet_hr18_512x512_40k_voc12aug_20200614_015958-714302be.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr18_512x512_40k_voc12aug/ocrnet_hr18_512x512_40k_voc12aug_20200614_015958.log.json
  Paper:
    Title: Object-Contextual Representations for Semantic Segmentation
    URL: https://arxiv.org/abs/1909.11065
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/ocr_head.py#L86
  Framework: PyTorch
- Name: ocrnet_hr48_4xb4-40k_voc12aug-512x512
  In Collection: OCRNet
  Results:
    Task: Semantic Segmentation
    Dataset: Pascal VOC 2012 + Aug
    Metrics:
      mIoU: 77.14
      mIoU(ms+flip): 79.71
  Config: configs/ocrnet/ocrnet_hr48_4xb4-40k_voc12aug-512x512.py
  Metadata:
    Training Data: Pascal VOC 2012 + Aug
    Batch Size: 16
    Architecture:
    - HRNetV2p-W48
    - OCRNet
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr48_512x512_40k_voc12aug/ocrnet_hr48_512x512_40k_voc12aug_20200614_015958-255bc5ce.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr48_512x512_40k_voc12aug/ocrnet_hr48_512x512_40k_voc12aug_20200614_015958.log.json
  Paper:
    Title: Object-Contextual Representations for Semantic Segmentation
    URL: https://arxiv.org/abs/1909.11065
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/ocr_head.py#L86
  Framework: PyTorch
