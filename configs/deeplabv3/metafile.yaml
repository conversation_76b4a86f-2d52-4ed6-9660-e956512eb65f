Collections:
- Name: DeepLabV3
  License: Apache License 2.0
  Metadata:
    Training Data:
    - Cityscapes
    - ADE20K
    - Pascal VOC 2012 + Aug
    - <PERSON> Context
    - Pascal Context 59
    - COCO-Stuff 10k
    - COCO-Stuff 164k
  Paper:
    Title: Rethinking atrous convolution for semantic image segmentation
    URL: https://arxiv.org/abs/1706.05587
  README: configs/deeplabv3/README.md
  Frameworks:
  - PyTorch
Models:
- Name: deeplabv3_r50-d8_4xb2-40k_cityscapes-512x1024
  In Collection: DeepLabV3
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 79.09
      mIoU(ms+flip): 80.45
  Config: configs/deeplabv3/deeplabv3_r50-d8_4xb2-40k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-50-D8
    - DeepLabV3
    Training Resources: 4x V100 GPUS
    Memory (GB): 6.1
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r50-d8_512x1024_40k_cityscapes/deeplabv3_r50-d8_512x1024_40k_cityscapes_20200605_022449-acadc2f8.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r50-d8_512x1024_40k_cityscapes/deeplabv3_r50-d8_512x1024_40k_cityscapes_20200605_022449.log.json
  Paper:
    Title: Rethinking atrous convolution for semantic image segmentation
    URL: https://arxiv.org/abs/1706.05587
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/aspp_head.py#L54
  Framework: PyTorch
- Name: deeplabv3_r101-d8_4xb2-40k_cityscapes-512x1024
  In Collection: DeepLabV3
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 77.12
      mIoU(ms+flip): 79.61
  Config: configs/deeplabv3/deeplabv3_r101-d8_4xb2-40k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-101-D8
    - DeepLabV3
    Training Resources: 4x V100 GPUS
    Memory (GB): 9.6
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_512x1024_40k_cityscapes/deeplabv3_r101-d8_512x1024_40k_cityscapes_20200605_012241-7fd3f799.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_512x1024_40k_cityscapes/deeplabv3_r101-d8_512x1024_40k_cityscapes_20200605_012241.log.json
  Paper:
    Title: Rethinking atrous convolution for semantic image segmentation
    URL: https://arxiv.org/abs/1706.05587
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/aspp_head.py#L54
  Framework: PyTorch
- Name: deeplabv3_r50-d8_4xb2-40k_cityscapes-769x769
  In Collection: DeepLabV3
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 78.58
      mIoU(ms+flip): 79.89
  Config: configs/deeplabv3/deeplabv3_r50-d8_4xb2-40k_cityscapes-769x769.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-50-D8
    - DeepLabV3
    Training Resources: 4x V100 GPUS
    Memory (GB): 6.9
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r50-d8_769x769_40k_cityscapes/deeplabv3_r50-d8_769x769_40k_cityscapes_20200606_113723-7eda553c.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r50-d8_769x769_40k_cityscapes/deeplabv3_r50-d8_769x769_40k_cityscapes_20200606_113723.log.json
  Paper:
    Title: Rethinking atrous convolution for semantic image segmentation
    URL: https://arxiv.org/abs/1706.05587
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/aspp_head.py#L54
  Framework: PyTorch
- Name: deeplabv3_r101-d8_4xb2-40k_cityscapes-769x769
  In Collection: DeepLabV3
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 79.27
      mIoU(ms+flip): 80.11
  Config: configs/deeplabv3/deeplabv3_r101-d8_4xb2-40k_cityscapes-769x769.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-101-D8
    - DeepLabV3
    Training Resources: 4x V100 GPUS
    Memory (GB): 10.9
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_769x769_40k_cityscapes/deeplabv3_r101-d8_769x769_40k_cityscapes_20200606_113809-c64f889f.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_769x769_40k_cityscapes/deeplabv3_r101-d8_769x769_40k_cityscapes_20200606_113809.log.json
  Paper:
    Title: Rethinking atrous convolution for semantic image segmentation
    URL: https://arxiv.org/abs/1706.05587
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/aspp_head.py#L54
  Framework: PyTorch
- Name: deeplabv3_r18-d8_4xb2-80k_cityscapes-512x1024
  In Collection: DeepLabV3
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 76.7
      mIoU(ms+flip): 78.27
  Config: configs/deeplabv3/deeplabv3_r18-d8_4xb2-80k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-18-D8
    - DeepLabV3
    Training Resources: 4x V100 GPUS
    Memory (GB): 1.7
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r18-d8_512x1024_80k_cityscapes/deeplabv3_r18-d8_512x1024_80k_cityscapes_20201225_021506-23dffbe2.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r18-d8_512x1024_80k_cityscapes/deeplabv3_r18-d8_512x1024_80k_cityscapes-20201225_021506.log.json
  Paper:
    Title: Rethinking atrous convolution for semantic image segmentation
    URL: https://arxiv.org/abs/1706.05587
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/aspp_head.py#L54
  Framework: PyTorch
- Name: deeplabv3_r50-d8_4xb2-80k_cityscapes-512x1024
  In Collection: DeepLabV3
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 79.32
      mIoU(ms+flip): 80.57
  Config: configs/deeplabv3/deeplabv3_r50-d8_4xb2-80k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-50-D8
    - DeepLabV3
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r50-d8_512x1024_80k_cityscapes/deeplabv3_r50-d8_512x1024_80k_cityscapes_20200606_113404-b92cfdd4.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r50-d8_512x1024_80k_cityscapes/deeplabv3_r50-d8_512x1024_80k_cityscapes_20200606_113404.log.json
  Paper:
    Title: Rethinking atrous convolution for semantic image segmentation
    URL: https://arxiv.org/abs/1706.05587
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/aspp_head.py#L54
  Framework: PyTorch
- Name: deeplabv3_r101-d8_4xb2-80k_cityscapes-512x1024
  In Collection: DeepLabV3
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 80.2
      mIoU(ms+flip): 81.21
  Config: configs/deeplabv3/deeplabv3_r101-d8_4xb2-80k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-101-D8
    - DeepLabV3
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_512x1024_80k_cityscapes/deeplabv3_r101-d8_512x1024_80k_cityscapes_20200606_113503-9e428899.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_512x1024_80k_cityscapes/deeplabv3_r101-d8_512x1024_80k_cityscapes_20200606_113503.log.json
  Paper:
    Title: Rethinking atrous convolution for semantic image segmentation
    URL: https://arxiv.org/abs/1706.05587
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/aspp_head.py#L54
  Framework: PyTorch
- Name: deeplabv3_r101-d8_4xb2-amp-80k_cityscapes-512x1024
  In Collection: DeepLabV3
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 80.48
  Config: configs/deeplabv3/deeplabv3_r101-d8_4xb2-amp-80k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-101-D8
    - DeepLabV3
    - (FP16)
    Training Resources: 4x V100 GPUS
    Memory (GB): 5.75
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_fp16_512x1024_80k_cityscapes/deeplabv3_r101-d8_fp16_512x1024_80k_cityscapes_20200717_230920-774d9cec.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_fp16_512x1024_80k_cityscapes/deeplabv3_r101-d8_fp16_512x1024_80k_cityscapes_20200717_230920.log.json
  Paper:
    Title: Rethinking atrous convolution for semantic image segmentation
    URL: https://arxiv.org/abs/1706.05587
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/aspp_head.py#L54
  Framework: PyTorch
- Name: deeplabv3_r18-d8_4xb2-80k_cityscapes-769x769
  In Collection: DeepLabV3
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 76.6
      mIoU(ms+flip): 78.26
  Config: configs/deeplabv3/deeplabv3_r18-d8_4xb2-80k_cityscapes-769x769.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-18-D8
    - DeepLabV3
    Training Resources: 4x V100 GPUS
    Memory (GB): 1.9
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r18-d8_769x769_80k_cityscapes/deeplabv3_r18-d8_769x769_80k_cityscapes_20201225_021506-6452126a.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r18-d8_769x769_80k_cityscapes/deeplabv3_r18-d8_769x769_80k_cityscapes-20201225_021506.log.json
  Paper:
    Title: Rethinking atrous convolution for semantic image segmentation
    URL: https://arxiv.org/abs/1706.05587
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/aspp_head.py#L54
  Framework: PyTorch
- Name: deeplabv3_r50-d8_4xb2-80k_cityscapes-769x769
  In Collection: DeepLabV3
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 79.89
      mIoU(ms+flip): 81.06
  Config: configs/deeplabv3/deeplabv3_r50-d8_4xb2-80k_cityscapes-769x769.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-50-D8
    - DeepLabV3
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r50-d8_769x769_80k_cityscapes/deeplabv3_r50-d8_769x769_80k_cityscapes_20200606_221338-788d6228.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r50-d8_769x769_80k_cityscapes/deeplabv3_r50-d8_769x769_80k_cityscapes_20200606_221338.log.json
  Paper:
    Title: Rethinking atrous convolution for semantic image segmentation
    URL: https://arxiv.org/abs/1706.05587
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/aspp_head.py#L54
  Framework: PyTorch
- Name: deeplabv3_r101-d8_4xb2-80k_cityscapes-769x769
  In Collection: DeepLabV3
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 79.67
      mIoU(ms+flip): 80.81
  Config: configs/deeplabv3/deeplabv3_r101-d8_4xb2-80k_cityscapes-769x769.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-101-D8
    - DeepLabV3
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_769x769_80k_cityscapes/deeplabv3_r101-d8_769x769_80k_cityscapes_20200607_013353-60e95418.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_769x769_80k_cityscapes/deeplabv3_r101-d8_769x769_80k_cityscapes_20200607_013353.log.json
  Paper:
    Title: Rethinking atrous convolution for semantic image segmentation
    URL: https://arxiv.org/abs/1706.05587
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/aspp_head.py#L54
  Framework: PyTorch
- Name: deeplabv3_r101-d16-mg124_4xb2-40k_cityscapes-512x1024
  In Collection: DeepLabV3
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 76.71
      mIoU(ms+flip): 78.63
  Config: configs/deeplabv3/deeplabv3_r101-d16-mg124_4xb2-40k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-101-D16-MG124
    - DeepLabV3
    Training Resources: 4x V100 GPUS
    Memory (GB): 4.7
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d16-mg124_512x1024_40k_cityscapes/deeplabv3_r101-d16-mg124_512x1024_40k_cityscapes_20200908_005644-67b0c992.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d16-mg124_512x1024_40k_cityscapes/deeplabv3_r101-d16-mg124_512x1024_40k_cityscapes-20200908_005644.log.json
  Paper:
    Title: Rethinking atrous convolution for semantic image segmentation
    URL: https://arxiv.org/abs/1706.05587
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/aspp_head.py#L54
  Framework: PyTorch
- Name: deeplabv3_r101-d16-mg124_4xb2-80k_cityscapes-512x1024
  In Collection: DeepLabV3
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 78.36
      mIoU(ms+flip): 79.84
  Config: configs/deeplabv3/deeplabv3_r101-d16-mg124_4xb2-80k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-101-D16-MG124
    - DeepLabV3
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d16-mg124_512x1024_80k_cityscapes/deeplabv3_r101-d16-mg124_512x1024_80k_cityscapes_20200908_005644-57bb8425.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d16-mg124_512x1024_80k_cityscapes/deeplabv3_r101-d16-mg124_512x1024_80k_cityscapes-20200908_005644.log.json
  Paper:
    Title: Rethinking atrous convolution for semantic image segmentation
    URL: https://arxiv.org/abs/1706.05587
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/aspp_head.py#L54
  Framework: PyTorch
- Name: deeplabv3_r18b-d8_4xb2-80k_cityscapes-512x1024
  In Collection: DeepLabV3
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 76.26
      mIoU(ms+flip): 77.88
  Config: configs/deeplabv3/deeplabv3_r18b-d8_4xb2-80k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-18b-D8
    - DeepLabV3
    Training Resources: 4x V100 GPUS
    Memory (GB): 1.6
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r18b-d8_512x1024_80k_cityscapes/deeplabv3_r18b-d8_512x1024_80k_cityscapes_20201225_094144-46040cef.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r18b-d8_512x1024_80k_cityscapes/deeplabv3_r18b-d8_512x1024_80k_cityscapes-20201225_094144.log.json
  Paper:
    Title: Rethinking atrous convolution for semantic image segmentation
    URL: https://arxiv.org/abs/1706.05587
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/aspp_head.py#L54
  Framework: PyTorch
- Name: deeplabv3_r50b-d8_4xb2-80k_cityscapes-512x1024
  In Collection: DeepLabV3
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 79.63
      mIoU(ms+flip): 80.98
  Config: configs/deeplabv3/deeplabv3_r50b-d8_4xb2-80k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-50b-D8
    - DeepLabV3
    Training Resources: 4x V100 GPUS
    Memory (GB): 6.0
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r50b-d8_512x1024_80k_cityscapes/deeplabv3_r50b-d8_512x1024_80k_cityscapes_20201225_155148-ec368954.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r50b-d8_512x1024_80k_cityscapes/deeplabv3_r50b-d8_512x1024_80k_cityscapes-20201225_155148.log.json
  Paper:
    Title: Rethinking atrous convolution for semantic image segmentation
    URL: https://arxiv.org/abs/1706.05587
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/aspp_head.py#L54
  Framework: PyTorch
- Name: deeplabv3_r101b-d8_4xb2-80k_cityscapes-512x1024
  In Collection: DeepLabV3
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 80.01
      mIoU(ms+flip): 81.21
  Config: configs/deeplabv3/deeplabv3_r101b-d8_4xb2-80k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-101b-D8
    - DeepLabV3
    Training Resources: 4x V100 GPUS
    Memory (GB): 9.5
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101b-d8_512x1024_80k_cityscapes/deeplabv3_r101b-d8_512x1024_80k_cityscapes_20201226_171821-8fd49503.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101b-d8_512x1024_80k_cityscapes/deeplabv3_r101b-d8_512x1024_80k_cityscapes-20201226_171821.log.json
  Paper:
    Title: Rethinking atrous convolution for semantic image segmentation
    URL: https://arxiv.org/abs/1706.05587
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/aspp_head.py#L54
  Framework: PyTorch
- Name: deeplabv3_r18b-d8_4xb2-80k_cityscapes-769x769
  In Collection: DeepLabV3
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 75.63
      mIoU(ms+flip): 77.51
  Config: configs/deeplabv3/deeplabv3_r18b-d8_4xb2-80k_cityscapes-769x769.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-18b-D8
    - DeepLabV3
    Training Resources: 4x V100 GPUS
    Memory (GB): 1.8
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r18b-d8_769x769_80k_cityscapes/deeplabv3_r18b-d8_769x769_80k_cityscapes_20201225_094144-fdc985d9.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r18b-d8_769x769_80k_cityscapes/deeplabv3_r18b-d8_769x769_80k_cityscapes-20201225_094144.log.json
  Paper:
    Title: Rethinking atrous convolution for semantic image segmentation
    URL: https://arxiv.org/abs/1706.05587
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/aspp_head.py#L54
  Framework: PyTorch
- Name: deeplabv3_r50b-d8_4xb2-80k_cityscapes-769x769
  In Collection: DeepLabV3
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 78.8
      mIoU(ms+flip): 80.27
  Config: configs/deeplabv3/deeplabv3_r50b-d8_4xb2-80k_cityscapes-769x769.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-50b-D8
    - DeepLabV3
    Training Resources: 4x V100 GPUS
    Memory (GB): 6.8
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r50b-d8_769x769_80k_cityscapes/deeplabv3_r50b-d8_769x769_80k_cityscapes_20201225_155404-87fb0cf4.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r50b-d8_769x769_80k_cityscapes/deeplabv3_r50b-d8_769x769_80k_cityscapes-20201225_155404.log.json
  Paper:
    Title: Rethinking atrous convolution for semantic image segmentation
    URL: https://arxiv.org/abs/1706.05587
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/aspp_head.py#L54
  Framework: PyTorch
- Name: deeplabv3_r101b-d8_4xb2-80k_cityscapes-769x769
  In Collection: DeepLabV3
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 79.41
      mIoU(ms+flip): 80.73
  Config: configs/deeplabv3/deeplabv3_r101b-d8_4xb2-80k_cityscapes-769x769.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-101b-D8
    - DeepLabV3
    Training Resources: 4x V100 GPUS
    Memory (GB): 10.7
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101b-d8_769x769_80k_cityscapes/deeplabv3_r101b-d8_769x769_80k_cityscapes_20201226_190843-9142ee57.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101b-d8_769x769_80k_cityscapes/deeplabv3_r101b-d8_769x769_80k_cityscapes-20201226_190843.log.json
  Paper:
    Title: Rethinking atrous convolution for semantic image segmentation
    URL: https://arxiv.org/abs/1706.05587
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/aspp_head.py#L54
  Framework: PyTorch
- Name: deeplabv3_r50-d8_4xb4-80k_ade20k-512x512
  In Collection: DeepLabV3
  Results:
    Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 42.42
      mIoU(ms+flip): 43.28
  Config: configs/deeplabv3/deeplabv3_r50-d8_4xb4-80k_ade20k-512x512.py
  Metadata:
    Training Data: ADE20K
    Batch Size: 16
    Architecture:
    - R-50-D8
    - DeepLabV3
    Training Resources: 4x V100 GPUS
    Memory (GB): 8.9
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r50-d8_512x512_80k_ade20k/deeplabv3_r50-d8_512x512_80k_ade20k_20200614_185028-0bb3f844.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r50-d8_512x512_80k_ade20k/deeplabv3_r50-d8_512x512_80k_ade20k_20200614_185028.log.json
  Paper:
    Title: Rethinking atrous convolution for semantic image segmentation
    URL: https://arxiv.org/abs/1706.05587
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/aspp_head.py#L54
  Framework: PyTorch
- Name: deeplabv3_r101-d8_4xb4-80k_ade20k-512x512
  In Collection: DeepLabV3
  Results:
    Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 44.08
      mIoU(ms+flip): 45.19
  Config: configs/deeplabv3/deeplabv3_r101-d8_4xb4-80k_ade20k-512x512.py
  Metadata:
    Training Data: ADE20K
    Batch Size: 16
    Architecture:
    - R-101-D8
    - DeepLabV3
    Training Resources: 4x V100 GPUS
    Memory (GB): 12.4
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_512x512_80k_ade20k/deeplabv3_r101-d8_512x512_80k_ade20k_20200615_021256-d89c7fa4.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_512x512_80k_ade20k/deeplabv3_r101-d8_512x512_80k_ade20k_20200615_021256.log.json
  Paper:
    Title: Rethinking atrous convolution for semantic image segmentation
    URL: https://arxiv.org/abs/1706.05587
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/aspp_head.py#L54
  Framework: PyTorch
- Name: deeplabv3_r50-d8_4xb4-160k_ade20k-512x512
  In Collection: DeepLabV3
  Results:
    Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 42.66
      mIoU(ms+flip): 44.09
  Config: configs/deeplabv3/deeplabv3_r50-d8_4xb4-160k_ade20k-512x512.py
  Metadata:
    Training Data: ADE20K
    Batch Size: 16
    Architecture:
    - R-50-D8
    - DeepLabV3
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r50-d8_512x512_160k_ade20k/deeplabv3_r50-d8_512x512_160k_ade20k_20200615_123227-5d0ee427.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r50-d8_512x512_160k_ade20k/deeplabv3_r50-d8_512x512_160k_ade20k_20200615_123227.log.json
  Paper:
    Title: Rethinking atrous convolution for semantic image segmentation
    URL: https://arxiv.org/abs/1706.05587
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/aspp_head.py#L54
  Framework: PyTorch
- Name: deeplabv3_r101-d8_4xb4-160k_ade20k-512x512
  In Collection: DeepLabV3
  Results:
    Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 45.0
      mIoU(ms+flip): 46.66
  Config: configs/deeplabv3/deeplabv3_r101-d8_4xb4-160k_ade20k-512x512.py
  Metadata:
    Training Data: ADE20K
    Batch Size: 16
    Architecture:
    - R-101-D8
    - DeepLabV3
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_512x512_160k_ade20k/deeplabv3_r101-d8_512x512_160k_ade20k_20200615_105816-b1f72b3b.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_512x512_160k_ade20k/deeplabv3_r101-d8_512x512_160k_ade20k_20200615_105816.log.json
  Paper:
    Title: Rethinking atrous convolution for semantic image segmentation
    URL: https://arxiv.org/abs/1706.05587
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/aspp_head.py#L54
  Framework: PyTorch
- Name: deeplabv3_r50-d8_4xb4-20k_voc12aug-512x512
  In Collection: DeepLabV3
  Results:
    Task: Semantic Segmentation
    Dataset: Pascal VOC 2012 + Aug
    Metrics:
      mIoU: 76.17
      mIoU(ms+flip): 77.42
  Config: configs/deeplabv3/deeplabv3_r50-d8_4xb4-20k_voc12aug-512x512.py
  Metadata:
    Training Data: Pascal VOC 2012 + Aug
    Batch Size: 16
    Architecture:
    - R-50-D8
    - DeepLabV3
    Training Resources: 4x V100 GPUS
    Memory (GB): 6.1
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r50-d8_512x512_20k_voc12aug/deeplabv3_r50-d8_512x512_20k_voc12aug_20200617_010906-596905ef.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r50-d8_512x512_20k_voc12aug/deeplabv3_r50-d8_512x512_20k_voc12aug_20200617_010906.log.json
  Paper:
    Title: Rethinking atrous convolution for semantic image segmentation
    URL: https://arxiv.org/abs/1706.05587
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/aspp_head.py#L54
  Framework: PyTorch
- Name: deeplabv3_r101-d8_4xb4-20k_voc12aug-512x512
  In Collection: DeepLabV3
  Results:
    Task: Semantic Segmentation
    Dataset: Pascal VOC 2012 + Aug
    Metrics:
      mIoU: 78.7
      mIoU(ms+flip): 79.95
  Config: configs/deeplabv3/deeplabv3_r101-d8_4xb4-20k_voc12aug-512x512.py
  Metadata:
    Training Data: Pascal VOC 2012 + Aug
    Batch Size: 16
    Architecture:
    - R-101-D8
    - DeepLabV3
    Training Resources: 4x V100 GPUS
    Memory (GB): 9.6
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_512x512_20k_voc12aug/deeplabv3_r101-d8_512x512_20k_voc12aug_20200617_010932-8d13832f.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_512x512_20k_voc12aug/deeplabv3_r101-d8_512x512_20k_voc12aug_20200617_010932.log.json
  Paper:
    Title: Rethinking atrous convolution for semantic image segmentation
    URL: https://arxiv.org/abs/1706.05587
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/aspp_head.py#L54
  Framework: PyTorch
- Name: deeplabv3_r50-d8_4xb4-40k_voc12aug-512x512
  In Collection: DeepLabV3
  Results:
    Task: Semantic Segmentation
    Dataset: Pascal VOC 2012 + Aug
    Metrics:
      mIoU: 77.68
      mIoU(ms+flip): 78.78
  Config: configs/deeplabv3/deeplabv3_r50-d8_4xb4-40k_voc12aug-512x512.py
  Metadata:
    Training Data: Pascal VOC 2012 + Aug
    Batch Size: 16
    Architecture:
    - R-50-D8
    - DeepLabV3
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r50-d8_512x512_40k_voc12aug/deeplabv3_r50-d8_512x512_40k_voc12aug_20200613_161546-2ae96e7e.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r50-d8_512x512_40k_voc12aug/deeplabv3_r50-d8_512x512_40k_voc12aug_20200613_161546.log.json
  Paper:
    Title: Rethinking atrous convolution for semantic image segmentation
    URL: https://arxiv.org/abs/1706.05587
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/aspp_head.py#L54
  Framework: PyTorch
- Name: deeplabv3_r101-d8_4xb4-40k_voc12aug-512x512
  In Collection: DeepLabV3
  Results:
    Task: Semantic Segmentation
    Dataset: Pascal VOC 2012 + Aug
    Metrics:
      mIoU: 77.92
      mIoU(ms+flip): 79.18
  Config: configs/deeplabv3/deeplabv3_r101-d8_4xb4-40k_voc12aug-512x512.py
  Metadata:
    Training Data: Pascal VOC 2012 + Aug
    Batch Size: 16
    Architecture:
    - R-101-D8
    - DeepLabV3
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_512x512_40k_voc12aug/deeplabv3_r101-d8_512x512_40k_voc12aug_20200613_161432-0017d784.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_512x512_40k_voc12aug/deeplabv3_r101-d8_512x512_40k_voc12aug_20200613_161432.log.json
  Paper:
    Title: Rethinking atrous convolution for semantic image segmentation
    URL: https://arxiv.org/abs/1706.05587
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/aspp_head.py#L54
  Framework: PyTorch
- Name: deeplabv3_r101-d8_4xb4-40k_pascal-context-480x480
  In Collection: DeepLabV3
  Results:
    Task: Semantic Segmentation
    Dataset: Pascal Context
    Metrics:
      mIoU: 46.55
      mIoU(ms+flip): 47.81
  Config: configs/deeplabv3/deeplabv3_r101-d8_4xb4-40k_pascal-context-480x480.py
  Metadata:
    Training Data: Pascal Context
    Batch Size: 16
    Architecture:
    - R-101-D8
    - DeepLabV3
    Training Resources: 4x V100 GPUS
    Memory (GB): 9.2
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_480x480_40k_pascal_context/deeplabv3_r101-d8_480x480_40k_pascal_context_20200911_204118-1aa27336.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_480x480_40k_pascal_context/deeplabv3_r101-d8_480x480_40k_pascal_context-20200911_204118.log.json
  Paper:
    Title: Rethinking atrous convolution for semantic image segmentation
    URL: https://arxiv.org/abs/1706.05587
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/aspp_head.py#L54
  Framework: PyTorch
- Name: deeplabv3_r101-d8_4xb4-80k_pascal-context-480x480
  In Collection: DeepLabV3
  Results:
    Task: Semantic Segmentation
    Dataset: Pascal Context
    Metrics:
      mIoU: 46.42
      mIoU(ms+flip): 47.53
  Config: configs/deeplabv3/deeplabv3_r101-d8_4xb4-80k_pascal-context-480x480.py
  Metadata:
    Training Data: Pascal Context
    Batch Size: 16
    Architecture:
    - R-101-D8
    - DeepLabV3
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_480x480_80k_pascal_context/deeplabv3_r101-d8_480x480_80k_pascal_context_20200911_170155-2a21fff3.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_480x480_80k_pascal_context/deeplabv3_r101-d8_480x480_80k_pascal_context-20200911_170155.log.json
  Paper:
    Title: Rethinking atrous convolution for semantic image segmentation
    URL: https://arxiv.org/abs/1706.05587
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/aspp_head.py#L54
  Framework: PyTorch
- Name: deeplabv3_r101-d8_4xb4-40k_pascal-context-59-480x480
  In Collection: DeepLabV3
  Results:
    Task: Semantic Segmentation
    Dataset: Pascal Context 59
    Metrics:
      mIoU: 52.61
      mIoU(ms+flip): 54.28
  Config: configs/deeplabv3/deeplabv3_r101-d8_4xb4-40k_pascal-context-59-480x480.py
  Metadata:
    Training Data: Pascal Context 59
    Batch Size: 16
    Architecture:
    - R-101-D8
    - DeepLabV3
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_480x480_40k_pascal_context_59/deeplabv3_r101-d8_480x480_40k_pascal_context_59_20210416_110332-cb08ea46.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_480x480_40k_pascal_context_59/deeplabv3_r101-d8_480x480_40k_pascal_context_59-20210416_110332.log.json
  Paper:
    Title: Rethinking atrous convolution for semantic image segmentation
    URL: https://arxiv.org/abs/1706.05587
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/aspp_head.py#L54
  Framework: PyTorch
- Name: deeplabv3_r101-d8_4xb4-80k_pascal-context-59-480x480
  In Collection: DeepLabV3
  Results:
    Task: Semantic Segmentation
    Dataset: Pascal Context 59
    Metrics:
      mIoU: 52.46
      mIoU(ms+flip): 54.09
  Config: configs/deeplabv3/deeplabv3_r101-d8_4xb4-80k_pascal-context-59-480x480.py
  Metadata:
    Training Data: Pascal Context 59
    Batch Size: 16
    Architecture:
    - R-101-D8
    - DeepLabV3
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_480x480_80k_pascal_context_59/deeplabv3_r101-d8_480x480_80k_pascal_context_59_20210416_113002-26303993.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_480x480_80k_pascal_context_59/deeplabv3_r101-d8_480x480_80k_pascal_context_59-20210416_113002.log.json
  Paper:
    Title: Rethinking atrous convolution for semantic image segmentation
    URL: https://arxiv.org/abs/1706.05587
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/aspp_head.py#L54
  Framework: PyTorch
- Name: deeplabv3_r50-d8_4xb4-20k_coco-stuff10k-512x512
  In Collection: DeepLabV3
  Results:
    Task: Semantic Segmentation
    Dataset: COCO-Stuff 10k
    Metrics:
      mIoU: 34.66
      mIoU(ms+flip): 36.08
  Config: configs/deeplabv3/deeplabv3_r50-d8_4xb4-20k_coco-stuff10k-512x512.py
  Metadata:
    Training Data: COCO-Stuff 10k
    Batch Size: 16
    Architecture:
    - R-50-D8
    - DeepLabV3
    Training Resources: 4x V100 GPUS
    Memory (GB): 9.6
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r50-d8_512x512_4x4_20k_coco-stuff10k/deeplabv3_r50-d8_512x512_4x4_20k_coco-stuff10k_20210821_043025-b35f789d.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r50-d8_512x512_4x4_20k_coco-stuff10k/deeplabv3_r50-d8_512x512_4x4_20k_coco-stuff10k_20210821_043025.log.json
  Paper:
    Title: Rethinking atrous convolution for semantic image segmentation
    URL: https://arxiv.org/abs/1706.05587
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/aspp_head.py#L54
  Framework: PyTorch
- Name: deeplabv3_r101-d8_4xb4-20k_coco-stuff10k-512x512
  In Collection: DeepLabV3
  Results:
    Task: Semantic Segmentation
    Dataset: COCO-Stuff 10k
    Metrics:
      mIoU: 37.3
      mIoU(ms+flip): 38.42
  Config: configs/deeplabv3/deeplabv3_r101-d8_4xb4-20k_coco-stuff10k-512x512.py
  Metadata:
    Training Data: COCO-Stuff 10k
    Batch Size: 16
    Architecture:
    - R-101-D8
    - DeepLabV3
    Training Resources: 4x V100 GPUS
    Memory (GB): 13.2
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_512x512_4x4_20k_coco-stuff10k/deeplabv3_r101-d8_512x512_4x4_20k_coco-stuff10k_20210821_043025-c49752cb.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_512x512_4x4_20k_coco-stuff10k/deeplabv3_r101-d8_512x512_4x4_20k_coco-stuff10k_20210821_043025.log.json
  Paper:
    Title: Rethinking atrous convolution for semantic image segmentation
    URL: https://arxiv.org/abs/1706.05587
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/aspp_head.py#L54
  Framework: PyTorch
- Name: deeplabv3_r50-d8_4xb4-40k_coco-stuff10k-512x512
  In Collection: DeepLabV3
  Results:
    Task: Semantic Segmentation
    Dataset: COCO-Stuff 10k
    Metrics:
      mIoU: 35.73
      mIoU(ms+flip): 37.09
  Config: configs/deeplabv3/deeplabv3_r50-d8_4xb4-40k_coco-stuff10k-512x512.py
  Metadata:
    Training Data: COCO-Stuff 10k
    Batch Size: 16
    Architecture:
    - R-50-D8
    - DeepLabV3
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r50-d8_512x512_4x4_40k_coco-stuff10k/deeplabv3_r50-d8_512x512_4x4_40k_coco-stuff10k_20210821_043305-dc76f3ff.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r50-d8_512x512_4x4_40k_coco-stuff10k/deeplabv3_r50-d8_512x512_4x4_40k_coco-stuff10k_20210821_043305.log.json
  Paper:
    Title: Rethinking atrous convolution for semantic image segmentation
    URL: https://arxiv.org/abs/1706.05587
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/aspp_head.py#L54
  Framework: PyTorch
- Name: deeplabv3_r101-d8_4xb4-40k_coco-stuff10k-512x512
  In Collection: DeepLabV3
  Results:
    Task: Semantic Segmentation
    Dataset: COCO-Stuff 10k
    Metrics:
      mIoU: 37.81
      mIoU(ms+flip): 38.8
  Config: configs/deeplabv3/deeplabv3_r101-d8_4xb4-40k_coco-stuff10k-512x512.py
  Metadata:
    Training Data: COCO-Stuff 10k
    Batch Size: 16
    Architecture:
    - R-101-D8
    - DeepLabV3
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_512x512_4x4_40k_coco-stuff10k/deeplabv3_r101-d8_512x512_4x4_40k_coco-stuff10k_20210821_043305-636cb433.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_512x512_4x4_40k_coco-stuff10k/deeplabv3_r101-d8_512x512_4x4_40k_coco-stuff10k_20210821_043305.log.json
  Paper:
    Title: Rethinking atrous convolution for semantic image segmentation
    URL: https://arxiv.org/abs/1706.05587
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/aspp_head.py#L54
  Framework: PyTorch
- Name: deeplabv3_r50-d8_4xb4-80k_coco-stuff164k-512x512
  In Collection: DeepLabV3
  Results:
    Task: Semantic Segmentation
    Dataset: COCO-Stuff 164k
    Metrics:
      mIoU: 39.38
      mIoU(ms+flip): 40.03
  Config: configs/deeplabv3/deeplabv3_r50-d8_4xb4-80k_coco-stuff164k-512x512.py
  Metadata:
    Training Data: COCO-Stuff 164k
    Batch Size: 16
    Architecture:
    - R-50-D8
    - DeepLabV3
    Training Resources: 4x V100 GPUS
    Memory (GB): 9.6
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r50-d8_512x512_4x4_80k_coco-stuff164k/deeplabv3_r50-d8_512x512_4x4_80k_coco-stuff164k_20210709_163016-88675c24.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r50-d8_512x512_4x4_80k_coco-stuff164k/deeplabv3_r50-d8_512x512_4x4_80k_coco-stuff164k_20210709_163016.log.json
  Paper:
    Title: Rethinking atrous convolution for semantic image segmentation
    URL: https://arxiv.org/abs/1706.05587
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/aspp_head.py#L54
  Framework: PyTorch
- Name: deeplabv3_r101-d8_4xb4-80k_coco-stuff164k-512x512
  In Collection: DeepLabV3
  Results:
    Task: Semantic Segmentation
    Dataset: COCO-Stuff 164k
    Metrics:
      mIoU: 40.87
      mIoU(ms+flip): 41.5
  Config: configs/deeplabv3/deeplabv3_r101-d8_4xb4-80k_coco-stuff164k-512x512.py
  Metadata:
    Training Data: COCO-Stuff 164k
    Batch Size: 16
    Architecture:
    - R-101-D8
    - DeepLabV3
    Training Resources: 4x V100 GPUS
    Memory (GB): 13.2
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_512x512_4x4_80k_coco-stuff164k/deeplabv3_r101-d8_512x512_4x4_80k_coco-stuff164k_20210709_201252-13600dc2.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_512x512_4x4_80k_coco-stuff164k/deeplabv3_r101-d8_512x512_4x4_80k_coco-stuff164k_20210709_201252.log.json
  Paper:
    Title: Rethinking atrous convolution for semantic image segmentation
    URL: https://arxiv.org/abs/1706.05587
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/aspp_head.py#L54
  Framework: PyTorch
- Name: deeplabv3_r50-d8_4xb4-160k_coco-stuff164k-512x512
  In Collection: DeepLabV3
  Results:
    Task: Semantic Segmentation
    Dataset: COCO-Stuff 164k
    Metrics:
      mIoU: 41.09
      mIoU(ms+flip): 41.69
  Config: configs/deeplabv3/deeplabv3_r50-d8_4xb4-160k_coco-stuff164k-512x512.py
  Metadata:
    Training Data: COCO-Stuff 164k
    Batch Size: 16
    Architecture:
    - R-50-D8
    - DeepLabV3
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r50-d8_512x512_4x4_160k_coco-stuff164k/deeplabv3_r50-d8_512x512_4x4_160k_coco-stuff164k_20210709_163016-49f2812b.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r50-d8_512x512_4x4_160k_coco-stuff164k/deeplabv3_r50-d8_512x512_4x4_160k_coco-stuff164k_20210709_163016.log.json
  Paper:
    Title: Rethinking atrous convolution for semantic image segmentation
    URL: https://arxiv.org/abs/1706.05587
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/aspp_head.py#L54
  Framework: PyTorch
- Name: deeplabv3_r101-d8_4xb4-160k_coco-stuff164k-512x512
  In Collection: DeepLabV3
  Results:
    Task: Semantic Segmentation
    Dataset: COCO-Stuff 164k
    Metrics:
      mIoU: 41.82
      mIoU(ms+flip): 42.49
  Config: configs/deeplabv3/deeplabv3_r101-d8_4xb4-160k_coco-stuff164k-512x512.py
  Metadata:
    Training Data: COCO-Stuff 164k
    Batch Size: 16
    Architecture:
    - R-101-D8
    - DeepLabV3
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_512x512_4x4_160k_coco-stuff164k/deeplabv3_r101-d8_512x512_4x4_160k_coco-stuff164k_20210709_155402-f035acfd.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_512x512_4x4_160k_coco-stuff164k/deeplabv3_r101-d8_512x512_4x4_160k_coco-stuff164k_20210709_155402.log.json
  Paper:
    Title: Rethinking atrous convolution for semantic image segmentation
    URL: https://arxiv.org/abs/1706.05587
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/aspp_head.py#L54
  Framework: PyTorch
- Name: deeplabv3_r50-d8_4xb4-320k_coco-stuff164k-512x512
  In Collection: DeepLabV3
  Results:
    Task: Semantic Segmentation
    Dataset: COCO-Stuff 164k
    Metrics:
      mIoU: 41.37
      mIoU(ms+flip): 42.22
  Config: configs/deeplabv3/deeplabv3_r50-d8_4xb4-320k_coco-stuff164k-512x512.py
  Metadata:
    Training Data: COCO-Stuff 164k
    Batch Size: 16
    Architecture:
    - R-50-D8
    - DeepLabV3
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r50-d8_512x512_4x4_320k_coco-stuff164k/deeplabv3_r50-d8_512x512_4x4_320k_coco-stuff164k_20210709_155403-51b21115.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r50-d8_512x512_4x4_320k_coco-stuff164k/deeplabv3_r50-d8_512x512_4x4_320k_coco-stuff164k_20210709_155403.log.json
  Paper:
    Title: Rethinking atrous convolution for semantic image segmentation
    URL: https://arxiv.org/abs/1706.05587
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/aspp_head.py#L54
  Framework: PyTorch
- Name: deeplabv3_r101-d8_4xb4-320k_coco-stuff164k-512x512
  In Collection: DeepLabV3
  Results:
    Task: Semantic Segmentation
    Dataset: COCO-Stuff 164k
    Metrics:
      mIoU: 42.61
      mIoU(ms+flip): 43.42
  Config: configs/deeplabv3/deeplabv3_r101-d8_4xb4-320k_coco-stuff164k-512x512.py
  Metadata:
    Training Data: COCO-Stuff 164k
    Batch Size: 16
    Architecture:
    - R-101-D8
    - DeepLabV3
    Training Resources: 4x V100 GPUS
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_512x512_4x4_320k_coco-stuff164k/deeplabv3_r101-d8_512x512_4x4_320k_coco-stuff164k_20210709_155402-3cbca14d.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_512x512_4x4_320k_coco-stuff164k/deeplabv3_r101-d8_512x512_4x4_320k_coco-stuff164k_20210709_155402.log.json
  Paper:
    Title: Rethinking atrous convolution for semantic image segmentation
    URL: https://arxiv.org/abs/1706.05587
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/aspp_head.py#L54
  Framework: PyTorch
