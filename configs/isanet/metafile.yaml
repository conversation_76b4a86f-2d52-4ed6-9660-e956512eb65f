Collections:
- Name: ISANet
  License: Apache License 2.0
  Metadata:
    Training Data:
    - Cityscapes
    - ADE20K
    - Pascal VOC 2012 + Aug
  Paper:
    Title: Interlaced Sparse Self-Attention for Semantic Segmentation
    URL: https://arxiv.org/abs/1907.12273
  README: configs/isanet/README.md
  Frameworks:
  - PyTorch
Models:
- Name: isanet_r50-d8_4xb2-40k_cityscapes-512x1024
  In Collection: ISANet
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 78.49
      mIoU(ms+flip): 79.44
  Config: configs/isanet/isanet_r50-d8_4xb2-40k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-50-D8
    - ISANet
    Training Resources: 4x V100 GPUS
    Memory (GB): 5.869
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/isanet/isanet_r50-d8_512x1024_40k_cityscapes/isanet_r50-d8_512x1024_40k_cityscapes_20210901_054739-981bd763.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/isanet/isanet_r50-d8_512x1024_40k_cityscapes/isanet_r50-d8_512x1024_40k_cityscapes_20210901_054739.log.json
  Paper:
    Title: Interlaced Sparse Self-Attention for Semantic Segmentation
    URL: https://arxiv.org/abs/1907.12273
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.18.0/mmseg/models/decode_heads/isa_head.py#L58
  Framework: PyTorch
- Name: isanet_r50-d8_4xb2-80k_cityscapes-512x1024
  In Collection: ISANet
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 78.68
      mIoU(ms+flip): 80.25
  Config: configs/isanet/isanet_r50-d8_4xb2-80k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-50-D8
    - ISANet
    Training Resources: 4x V100 GPUS
    Memory (GB): 5.869
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/isanet/isanet_r50-d8_512x1024_80k_cityscapes/isanet_r50-d8_512x1024_80k_cityscapes_20210901_074202-89384497.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/isanet/isanet_r50-d8_512x1024_80k_cityscapes/isanet_r50-d8_512x1024_80k_cityscapes_20210901_074202.log.json
  Paper:
    Title: Interlaced Sparse Self-Attention for Semantic Segmentation
    URL: https://arxiv.org/abs/1907.12273
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.18.0/mmseg/models/decode_heads/isa_head.py#L58
  Framework: PyTorch
- Name: isanet_r50-d8_4xb2-40k_cityscapes-769x769
  In Collection: ISANet
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 78.7
      mIoU(ms+flip): 80.28
  Config: configs/isanet/isanet_r50-d8_4xb2-40k_cityscapes-769x769.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-50-D8
    - ISANet
    Training Resources: 4x V100 GPUS
    Memory (GB): 6.759
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/isanet/isanet_r50-d8_769x769_40k_cityscapes/isanet_r50-d8_769x769_40k_cityscapes_20210903_050200-4ae7e65b.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/isanet/isanet_r50-d8_769x769_40k_cityscapes/isanet_r50-d8_769x769_40k_cityscapes_20210903_050200.log.json
  Paper:
    Title: Interlaced Sparse Self-Attention for Semantic Segmentation
    URL: https://arxiv.org/abs/1907.12273
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.18.0/mmseg/models/decode_heads/isa_head.py#L58
  Framework: PyTorch
- Name: isanet_r50-d8_4xb2-80k_cityscapes-769x769
  In Collection: ISANet
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 79.29
      mIoU(ms+flip): 80.53
  Config: configs/isanet/isanet_r50-d8_4xb2-80k_cityscapes-769x769.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-50-D8
    - ISANet
    Training Resources: 4x V100 GPUS
    Memory (GB): 6.759
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/isanet/isanet_r50-d8_769x769_80k_cityscapes/isanet_r50-d8_769x769_80k_cityscapes_20210903_101126-99b54519.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/isanet/isanet_r50-d8_769x769_80k_cityscapes/isanet_r50-d8_769x769_80k_cityscapes_20210903_101126.log.json
  Paper:
    Title: Interlaced Sparse Self-Attention for Semantic Segmentation
    URL: https://arxiv.org/abs/1907.12273
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.18.0/mmseg/models/decode_heads/isa_head.py#L58
  Framework: PyTorch
- Name: isanet_r101-d8_4xb2-40k_cityscapes-512x1024
  In Collection: ISANet
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 79.58
      mIoU(ms+flip): 81.05
  Config: configs/isanet/isanet_r101-d8_4xb2-40k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-101-D8
    - ISANet
    Training Resources: 4x V100 GPUS
    Memory (GB): 9.425
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/isanet/isanet_r101-d8_512x1024_40k_cityscapes/isanet_r101-d8_512x1024_40k_cityscapes_20210901_145553-293e6bd6.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/isanet/isanet_r101-d8_512x1024_40k_cityscapes/isanet_r101-d8_512x1024_40k_cityscapes_20210901_145553.log.json
  Paper:
    Title: Interlaced Sparse Self-Attention for Semantic Segmentation
    URL: https://arxiv.org/abs/1907.12273
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.18.0/mmseg/models/decode_heads/isa_head.py#L58
  Framework: PyTorch
- Name: isanet_r101-d8_4xb2-80k_cityscapes-512x1024
  In Collection: ISANet
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 80.32
      mIoU(ms+flip): 81.58
  Config: configs/isanet/isanet_r101-d8_4xb2-80k_cityscapes-512x1024.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-101-D8
    - ISANet
    Training Resources: 4x V100 GPUS
    Memory (GB): 9.425
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/isanet/isanet_r101-d8_512x1024_80k_cityscapes/isanet_r101-d8_512x1024_80k_cityscapes_20210901_145243-5b99c9b2.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/isanet/isanet_r101-d8_512x1024_80k_cityscapes/isanet_r101-d8_512x1024_80k_cityscapes_20210901_145243.log.json
  Paper:
    Title: Interlaced Sparse Self-Attention for Semantic Segmentation
    URL: https://arxiv.org/abs/1907.12273
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.18.0/mmseg/models/decode_heads/isa_head.py#L58
  Framework: PyTorch
- Name: isanet_r101-d8_4xb2-40k_cityscapes-769x769
  In Collection: ISANet
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 79.68
      mIoU(ms+flip): 80.95
  Config: configs/isanet/isanet_r101-d8_4xb2-40k_cityscapes-769x769.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-101-D8
    - ISANet
    Training Resources: 4x V100 GPUS
    Memory (GB): 10.815
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/isanet/isanet_r101-d8_769x769_40k_cityscapes/isanet_r101-d8_769x769_40k_cityscapes_20210903_111320-509e7224.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/isanet/isanet_r101-d8_769x769_40k_cityscapes/isanet_r101-d8_769x769_40k_cityscapes_20210903_111320.log.json
  Paper:
    Title: Interlaced Sparse Self-Attention for Semantic Segmentation
    URL: https://arxiv.org/abs/1907.12273
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.18.0/mmseg/models/decode_heads/isa_head.py#L58
  Framework: PyTorch
- Name: isanet_r101-d8_4xb2-80k_cityscapes-769x769
  In Collection: ISANet
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 80.61
      mIoU(ms+flip): 81.59
  Config: configs/isanet/isanet_r101-d8_4xb2-80k_cityscapes-769x769.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - R-101-D8
    - ISANet
    Training Resources: 4x V100 GPUS
    Memory (GB): 10.815
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/isanet/isanet_r101-d8_769x769_80k_cityscapes/isanet_r101-d8_769x769_80k_cityscapes_20210903_111319-24f71dfa.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/isanet/isanet_r101-d8_769x769_80k_cityscapes/isanet_r101-d8_769x769_80k_cityscapes_20210903_111319.log.json
  Paper:
    Title: Interlaced Sparse Self-Attention for Semantic Segmentation
    URL: https://arxiv.org/abs/1907.12273
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.18.0/mmseg/models/decode_heads/isa_head.py#L58
  Framework: PyTorch
- Name: isanet_r50-d8_4xb4-80k_ade20k-512x512
  In Collection: ISANet
  Results:
    Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 41.12
      mIoU(ms+flip): 42.35
  Config: configs/isanet/isanet_r50-d8_4xb4-80k_ade20k-512x512.py
  Metadata:
    Training Data: ADE20K
    Batch Size: 16
    Architecture:
    - R-50-D8
    - ISANet
    Training Resources: 4x V100 GPUS
    Memory (GB): 9.0
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/isanet/isanet_r50-d8_512x512_80k_ade20k/isanet_r50-d8_512x512_80k_ade20k_20210903_124557-6ed83a0c.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/isanet/isanet_r50-d8_512x512_80k_ade20k/isanet_r50-d8_512x512_80k_ade20k_20210903_124557.log.json
  Paper:
    Title: Interlaced Sparse Self-Attention for Semantic Segmentation
    URL: https://arxiv.org/abs/1907.12273
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.18.0/mmseg/models/decode_heads/isa_head.py#L58
  Framework: PyTorch
- Name: isanet_r50-d8_4xb4-160k_ade20k-512x512
  In Collection: ISANet
  Results:
    Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 42.59
      mIoU(ms+flip): 43.07
  Config: configs/isanet/isanet_r50-d8_4xb4-160k_ade20k-512x512.py
  Metadata:
    Training Data: ADE20K
    Batch Size: 16
    Architecture:
    - R-50-D8
    - ISANet
    Training Resources: 4x V100 GPUS
    Memory (GB): 9.0
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/isanet/isanet_r50-d8_512x512_160k_ade20k/isanet_r50-d8_512x512_160k_ade20k_20210903_104850-f752d0a3.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/isanet/isanet_r50-d8_512x512_160k_ade20k/isanet_r50-d8_512x512_160k_ade20k_20210903_104850.log.json
  Paper:
    Title: Interlaced Sparse Self-Attention for Semantic Segmentation
    URL: https://arxiv.org/abs/1907.12273
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.18.0/mmseg/models/decode_heads/isa_head.py#L58
  Framework: PyTorch
- Name: isanet_r101-d8_4xb4-80k_ade20k-512x512
  In Collection: ISANet
  Results:
    Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 43.51
      mIoU(ms+flip): 44.38
  Config: configs/isanet/isanet_r101-d8_4xb4-80k_ade20k-512x512.py
  Metadata:
    Training Data: ADE20K
    Batch Size: 16
    Architecture:
    - R-101-D8
    - ISANet
    Training Resources: 4x V100 GPUS
    Memory (GB): 12.562
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/isanet/isanet_r101-d8_512x512_80k_ade20k/isanet_r101-d8_512x512_80k_ade20k_20210903_162056-68b235c2.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/isanet/isanet_r101-d8_512x512_80k_ade20k/isanet_r101-d8_512x512_80k_ade20k_20210903_162056.log.json
  Paper:
    Title: Interlaced Sparse Self-Attention for Semantic Segmentation
    URL: https://arxiv.org/abs/1907.12273
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.18.0/mmseg/models/decode_heads/isa_head.py#L58
  Framework: PyTorch
- Name: isanet_r101-d8_4xb4-160k_ade20k-512x512
  In Collection: ISANet
  Results:
    Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 43.8
      mIoU(ms+flip): 45.4
  Config: configs/isanet/isanet_r101-d8_4xb4-160k_ade20k-512x512.py
  Metadata:
    Training Data: ADE20K
    Batch Size: 16
    Architecture:
    - R-101-D8
    - ISANet
    Training Resources: 4x V100 GPUS
    Memory (GB): 12.562
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/isanet/isanet_r101-d8_512x512_160k_ade20k/isanet_r101-d8_512x512_160k_ade20k_20210903_211431-a7879dcd.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/isanet/isanet_r101-d8_512x512_160k_ade20k/isanet_r101-d8_512x512_160k_ade20k_20210903_211431.log.json
  Paper:
    Title: Interlaced Sparse Self-Attention for Semantic Segmentation
    URL: https://arxiv.org/abs/1907.12273
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.18.0/mmseg/models/decode_heads/isa_head.py#L58
  Framework: PyTorch
- Name: isanet_r50-d8_4xb4-20k_voc12aug-512x512
  In Collection: ISANet
  Results:
    Task: Semantic Segmentation
    Dataset: Pascal VOC 2012 + Aug
    Metrics:
      mIoU: 76.78
      mIoU(ms+flip): 77.79
  Config: configs/isanet/isanet_r50-d8_4xb4-20k_voc12aug-512x512.py
  Metadata:
    Training Data: Pascal VOC 2012 + Aug
    Batch Size: 16
    Architecture:
    - R-50-D8
    - ISANet
    Training Resources: 4x V100 GPUS
    Memory (GB): 5.9
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/isanet/isanet_r50-d8_512x512_20k_voc12aug/isanet_r50-d8_512x512_20k_voc12aug_20210901_164838-79d59b80.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/isanet/isanet_r50-d8_512x512_20k_voc12aug/isanet_r50-d8_512x512_20k_voc12aug_20210901_164838.log.json
  Paper:
    Title: Interlaced Sparse Self-Attention for Semantic Segmentation
    URL: https://arxiv.org/abs/1907.12273
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.18.0/mmseg/models/decode_heads/isa_head.py#L58
  Framework: PyTorch
- Name: isanet_r50-d8_4xb4-40k_voc12aug-512x512
  In Collection: ISANet
  Results:
    Task: Semantic Segmentation
    Dataset: Pascal VOC 2012 + Aug
    Metrics:
      mIoU: 76.2
      mIoU(ms+flip): 77.22
  Config: configs/isanet/isanet_r50-d8_4xb4-40k_voc12aug-512x512.py
  Metadata:
    Training Data: Pascal VOC 2012 + Aug
    Batch Size: 16
    Architecture:
    - R-50-D8
    - ISANet
    Training Resources: 4x V100 GPUS
    Memory (GB): 5.9
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/isanet/isanet_r50-d8_512x512_40k_voc12aug/isanet_r50-d8_512x512_40k_voc12aug_20210901_151349-7d08a54e.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/isanet/isanet_r50-d8_512x512_40k_voc12aug/isanet_r50-d8_512x512_40k_voc12aug_20210901_151349.log.json
  Paper:
    Title: Interlaced Sparse Self-Attention for Semantic Segmentation
    URL: https://arxiv.org/abs/1907.12273
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.18.0/mmseg/models/decode_heads/isa_head.py#L58
  Framework: PyTorch
- Name: isanet_r101-d8_4xb4-20k_voc12aug-512x512
  In Collection: ISANet
  Results:
    Task: Semantic Segmentation
    Dataset: Pascal VOC 2012 + Aug
    Metrics:
      mIoU: 78.46
      mIoU(ms+flip): 79.16
  Config: configs/isanet/isanet_r101-d8_4xb4-20k_voc12aug-512x512.py
  Metadata:
    Training Data: Pascal VOC 2012 + Aug
    Batch Size: 16
    Architecture:
    - R-101-D8
    - ISANet
    Training Resources: 4x V100 GPUS
    Memory (GB): 9.465
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/isanet/isanet_r101-d8_512x512_20k_voc12aug/isanet_r101-d8_512x512_20k_voc12aug_20210901_115805-3ccbf355.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/isanet/isanet_r101-d8_512x512_20k_voc12aug/isanet_r101-d8_512x512_20k_voc12aug_20210901_115805.log.json
  Paper:
    Title: Interlaced Sparse Self-Attention for Semantic Segmentation
    URL: https://arxiv.org/abs/1907.12273
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.18.0/mmseg/models/decode_heads/isa_head.py#L58
  Framework: PyTorch
- Name: isanet_r101-d8_4xb4-40k_voc12aug-512x512
  In Collection: ISANet
  Results:
    Task: Semantic Segmentation
    Dataset: Pascal VOC 2012 + Aug
    Metrics:
      mIoU: 78.12
      mIoU(ms+flip): 79.04
  Config: configs/isanet/isanet_r101-d8_4xb4-40k_voc12aug-512x512.py
  Metadata:
    Training Data: Pascal VOC 2012 + Aug
    Batch Size: 16
    Architecture:
    - R-101-D8
    - ISANet
    Training Resources: 4x V100 GPUS
    Memory (GB): 9.465
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/isanet/isanet_r101-d8_512x512_40k_voc12aug/isanet_r101-d8_512x512_40k_voc12aug_20210901_145814-bc71233b.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/isanet/isanet_r101-d8_512x512_40k_voc12aug/isanet_r101-d8_512x512_40k_voc12aug_20210901_145814.log.json
  Paper:
    Title: Interlaced Sparse Self-Attention for Semantic Segmentation
    URL: https://arxiv.org/abs/1907.12273
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.18.0/mmseg/models/decode_heads/isa_head.py#L58
  Framework: PyTorch
