Collections:
- Name: SETR
  License: Apache License 2.0
  Metadata:
    Training Data:
    - ADE20K
    - Cityscapes
  Paper:
    Title: Rethinking Semantic Segmentation from a Sequence-to-Sequence Perspective
      with Transformers
    URL: https://arxiv.org/abs/2012.15840
  README: configs/setr/README.md
  Frameworks:
  - PyTorch
Models:
- Name: setr_vit-l_naive_8xb2-160k_ade20k-512x512
  In Collection: SETR
  Results:
    Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 48.28
      mIoU(ms+flip): 49.56
  Config: configs/setr/setr_vit-l_naive_8xb2-160k_ade20k-512x512.py
  Metadata:
    Training Data: ADE20K
    Batch Size: 16
    Architecture:
    - ViT-L
    - SETR
    - Naive
    Training Resources: 8x V100 GPUS
    Memory (GB): 18.4
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/setr/setr_naive_512x512_160k_b16_ade20k/setr_naive_512x512_160k_b16_ade20k_20210619_191258-061f24f5.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/setr/setr_naive_512x512_160k_b16_ade20k/setr_naive_512x512_160k_b16_ade20k_20210619_191258.log.json
  Paper:
    Title: Rethinking Semantic Segmentation from a Sequence-to-Sequence Perspective
      with Transformers
    URL: https://arxiv.org/abs/2012.15840
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/setr_up_head.py#L11
  Framework: PyTorch
- Name: setr_vit-l_pup_8xb2-160k_ade20k-512x512
  In Collection: SETR
  Results:
    Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 48.24
      mIoU(ms+flip): 49.99
  Config: configs/setr/setr_vit-l_pup_8xb2-160k_ade20k-512x512.py
  Metadata:
    Training Data: ADE20K
    Batch Size: 16
    Architecture:
    - ViT-L
    - SETR
    - PUP
    Training Resources: 8x V100 GPUS
    Memory (GB): 19.54
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/setr/setr_pup_512x512_160k_b16_ade20k/setr_pup_512x512_160k_b16_ade20k_20210619_191343-7e0ce826.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/setr/setr_pup_512x512_160k_b16_ade20k/setr_pup_512x512_160k_b16_ade20k_20210619_191343.log.json
  Paper:
    Title: Rethinking Semantic Segmentation from a Sequence-to-Sequence Perspective
      with Transformers
    URL: https://arxiv.org/abs/2012.15840
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/setr_up_head.py#L11
  Framework: PyTorch
- Name: setr_vit-l-mla_8xb1-160k_ade20k-512x512
  In Collection: SETR
  Results:
    Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 47.34
      mIoU(ms+flip): 49.05
  Config: configs/setr/setr_vit-l-mla_8xb1-160k_ade20k-512x512.py
  Metadata:
    Training Data: ADE20K
    Batch Size: 8
    Architecture:
    - ViT-L
    - SETR
    - MLA
    Training Resources: 8x V100 GPUS
    Memory (GB): 10.96
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/setr/setr_mla_512x512_160k_b8_ade20k/setr_mla_512x512_160k_b8_ade20k_20210619_191118-c6d21df0.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/setr/setr_mla_512x512_160k_b8_ade20k/setr_mla_512x512_160k_b8_ade20k_20210619_191118.log.json
  Paper:
    Title: Rethinking Semantic Segmentation from a Sequence-to-Sequence Perspective
      with Transformers
    URL: https://arxiv.org/abs/2012.15840
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/setr_up_head.py#L11
  Framework: PyTorch
- Name: setr_vit-l_mla_8xb2-160k_ade20k-512x512
  In Collection: SETR
  Results:
    Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 47.39
      mIoU(ms+flip): 49.37
  Config: configs/setr/setr_vit-l_mla_8xb2-160k_ade20k-512x512.py
  Metadata:
    Training Data: ADE20K
    Batch Size: 16
    Architecture:
    - ViT-L
    - SETR
    - MLA
    Training Resources: 8x V100 GPUS
    Memory (GB): 17.3
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/setr/setr_mla_512x512_160k_b16_ade20k/setr_mla_512x512_160k_b16_ade20k_20210619_191057-f9741de7.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/setr/setr_mla_512x512_160k_b16_ade20k/setr_mla_512x512_160k_b16_ade20k_20210619_191057.log.json
  Paper:
    Title: Rethinking Semantic Segmentation from a Sequence-to-Sequence Perspective
      with Transformers
    URL: https://arxiv.org/abs/2012.15840
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/setr_up_head.py#L11
  Framework: PyTorch
- Name: setr_vit-l_naive_8xb1-80k_cityscapes-768x768
  In Collection: SETR
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 78.1
      mIoU(ms+flip): 80.22
  Config: configs/setr/setr_vit-l_naive_8xb1-80k_cityscapes-768x768.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - ViT-L
    - SETR
    - Naive
    Training Resources: 8x V100 GPUS
    Memory (GB): 24.06
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/setr/setr_naive_vit-large_8x1_768x768_80k_cityscapes/setr_naive_vit-large_8x1_768x768_80k_cityscapes_20211123_000505-20728e80.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/setr/setr_naive_vit-large_8x1_768x768_80k_cityscapes/setr_naive_vit-large_8x1_768x768_80k_cityscapes_20211123_000505.log.json
  Paper:
    Title: Rethinking Semantic Segmentation from a Sequence-to-Sequence Perspective
      with Transformers
    URL: https://arxiv.org/abs/2012.15840
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/setr_up_head.py#L11
  Framework: PyTorch
- Name: setr_vit-l_pup_8xb1-80k_cityscapes-768x768
  In Collection: SETR
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 79.21
      mIoU(ms+flip): 81.02
  Config: configs/setr/setr_vit-l_pup_8xb1-80k_cityscapes-768x768.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - ViT-L
    - SETR
    - PUP
    Training Resources: 8x V100 GPUS
    Memory (GB): 27.96
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/setr/setr_pup_vit-large_8x1_768x768_80k_cityscapes/setr_pup_vit-large_8x1_768x768_80k_cityscapes_20211122_155115-f6f37b8f.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/setr/setr_pup_vit-large_8x1_768x768_80k_cityscapes/setr_pup_vit-large_8x1_768x768_80k_cityscapes_20211122_155115.log.json
  Paper:
    Title: Rethinking Semantic Segmentation from a Sequence-to-Sequence Perspective
      with Transformers
    URL: https://arxiv.org/abs/2012.15840
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/setr_up_head.py#L11
  Framework: PyTorch
- Name: setr_vit-l_mla_8xb1-80k_cityscapes-768x768
  In Collection: SETR
  Results:
    Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 77.0
      mIoU(ms+flip): 79.59
  Config: configs/setr/setr_vit-l_mla_8xb1-80k_cityscapes-768x768.py
  Metadata:
    Training Data: Cityscapes
    Batch Size: 8
    Architecture:
    - ViT-L
    - SETR
    - MLA
    Training Resources: 8x V100 GPUS
    Memory (GB): 24.1
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/setr/setr_mla_vit-large_8x1_768x768_80k_cityscapes/setr_mla_vit-large_8x1_768x768_80k_cityscapes_20211119_101003-7f8dccbe.pth
  Training log: https://download.openmmlab.com/mmsegmentation/v0.5/setr/setr_mla_vit-large_8x1_768x768_80k_cityscapes/setr_mla_vit-large_8x1_768x768_80k_cityscapes_20211119_101003.log.json
  Paper:
    Title: Rethinking Semantic Segmentation from a Sequence-to-Sequence Perspective
      with Transformers
    URL: https://arxiv.org/abs/2012.15840
  Code: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/setr_up_head.py#L11
  Framework: PyTorch
